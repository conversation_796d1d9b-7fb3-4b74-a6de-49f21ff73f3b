######################## default configuration ####################
# The base URL of your site (required). This will be prepended to all relative URLs.
baseURL = "https://example.org"
# Title of your website (required).
title = "Hugoplate"
# Your theme name
# theme = "hugoplate"
# Default time zone for time stamps; use any valid tz database name: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones#List
timeZone = "America/New_York"
# post excerpt
summaryLength = 10 # see https://gohugo.io/content-management/excerpts/
# disable language
disableLanguages = [
] # example: ["fr"] for disable french language. see https://gohugo.io/content-management/multilingual/
hasCJKLanguage = false #  If hasCJKLanguage true, auto-detect Chinese/Japanese/Korean Languages in the content. see: https://gohugo.io/getting-started/configuration/#hascjklanguage
# default language
defaultContentLanguage = 'en'
# defaultContentLanguageInSubdir need to be true if you want to use the language code as a subdirectory and language specific 404 page
defaultContentLanguageInSubdir = false

########################### Services #############################
[services]
[services.googleAnalytics]
ID = 'G-MEASUREMENT_ID' # see https://gohugo.io/templates/internal/#configure-google-analytics

[services.disqus]
shortname = 'themefisher-template' # we use disqus to show comments in blog posts . To install disqus please follow this tutorial https://portfolio.peter-baumgartner.net/2017/09/10/how-to-install-disqus-on-hugo/

########################## Permalinks ############################
[permalinks.page]
"pages" = "/:slugorcontentbasename/"


########################## Pagination ############################
[pagination]
disableAliases = false
pagerSize = 10
path = 'page'


############################# Build ##############################
[build]
[build.buildStats]
enable = true
[[build.cachebusters]]
source = 'assets/notwatching/hugo_stats\.json'
target = 'css'

############################# Modules ############################
[module]
[[module.mounts]]
source = 'assets'
target = 'assets'
[[module.mounts]]
disableWatch = true
source = 'hugo_stats.json'
target = 'assets/notwatching/hugo_stats.json'


############################# Outputs ############################
[outputs]
home = ["HTML", "RSS", "WebAppManifest", "SearchIndex"]

############################# Imaging ############################
[imaging]
# See https://github.com/disintegration/imaging
# Default JPEG or WebP quality setting. Default is 75.
quality = 80
resampleFilter = "Lanczos"

############################ Caches ##############################
[caches]
[caches.images]
dir = ":resourceDir/_gen"
maxAge = "720h"

[caches.assets]
dir = ":resourceDir/_gen"
maxAge = "720h"


############################ Markup ##############################
[markup]
[markup.goldmark.renderer]
unsafe = true

[markup.highlight]
style = 'monokai' # see https://xyproto.github.io/splash/docs/all.html
lineNumbersInTable = false

[markup.tableOfContents]
startLevel = 2
endLevel = 5
ordered = true


########################### Media types ###########################
[mediaTypes]
[mediaTypes."application/manifest+json"]
suffixes = ["webmanifest"]


########################### Output Format ##########################
[outputFormats]
[outputFormats.WebAppManifest]
mediaType = "application/manifest+json"
rel = "manifest"

[outputFormats.SearchIndex]
mediaType = "application/json"
baseName = "searchindex"
isPlainText = true
notAlternative = true


############################# Plugins ##############################

# CSS Plugins
[[params.plugins.css]]
link = "plugins/swiper/swiper-bundle.css"
lazy = true
[[params.plugins.css]]
link = "plugins/glightbox/glightbox.css"
lazy = true
[[params.plugins.css]]
link = "plugins/font-awesome/v6/brands.css"
lazy = true
[[params.plugins.css]]
link = "plugins/font-awesome/v6/solid.css"
lazy = true
[[params.plugins.css]]
link = "plugins/font-awesome/v6/icons.css"
lazy = true

# JS Plugins
[[params.plugins.js]]
link = "js/search.js"
lazy = false
[[params.plugins.js]]
link = "plugins/swiper/swiper-bundle.js"
lazy = false
[[params.plugins.js]]
link = "plugins/cookie.js"
lazy = false
[[params.plugins.js]]
link = "plugins/glightbox/glightbox.js"
lazy = true
[[params.plugins.js]]
link = "js/gallery-slider.js"
lazy = true
[[params.plugins.js]]
link = "js/accordion.js"
lazy = true
[[params.plugins.js]]
link = "js/tab.js"
lazy = true
[[params.plugins.js]]
link = "js/modal.js"
lazy = true
[[params.plugins.js]]
link = "plugins/youtube-lite.js"
lazy = true
