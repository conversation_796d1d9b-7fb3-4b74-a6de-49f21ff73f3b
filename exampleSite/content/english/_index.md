---
# Banner
banner:
  title: "The Ultimate Starter Template You Need To Start Your Hugo Project"
  content: "Hugoplate is a free starter template built with <PERSON> and <PERSON><PERSON><PERSON>CS<PERSON>, providing everything you need to jumpstart your Hugo project and save valuable time."
  image: "/images/banner.png"
  button:
    enable: true
    label: "Get Started For Free"
    link: "https://github.com/zeon-studio/hugoplate"

# Features
features:
  - title: "What's Included in Hugoplate"
    image: "/images/service-1.png"
    content: "Hugoplate is a comprehensive starter template that includes everything you need to get started with your Hugo project. What's Included in Hugoplate"
    bulletpoints:
      - "10+ Pre-build pages"
      - "95+ Google Pagespeed Score"
      - "Build with <PERSON> and <PERSON><PERSON><PERSON>CSS for easy and customizable styling"
      - "Fully responsive on all devices"
      - "SEO-optimized for better search engine rankings"
      - "**Open-source and free** for personal and commercial use"
    button:
      enable: false
      label: "Get Started Now"
      link: "#"

  - title: "Discover the Key Features Of Hugo"
    image: "/images/service-2.png"
    content: "<PERSON> is an all-in-one web framework for building fast, content-focused websites. It offers a range of exciting features for developers and website creators. Some of the key features are:"
    bulletpoints:
      - "Zero JS, by default: No JavaScript runtime overhead to slow you down."
      - "Customizable: Tailwind, MDX, and 100+ other integrations to choose from."
      - "UI-agnostic: Supports React, Preact, Svelte, Vue, Solid, Lit and more."
    button:
      enable: true
      label: "Get Started Now"
      link: "https://github.com/zeon-studio/hugoplate"

  - title: "The Top Reasons to Choose <PERSON> for Your Hugo Project"
    image: "/images/service-3.png"
    content: "With Hugo, you can build modern and content-focused websites without sacrificing performance or ease of use."
    bulletpoints:
      - "Instantly load static sites for better user experience and SEO."
      - "Intuitive syntax and support for popular frameworks make learning and using Hugo a breeze."
      - "Use any front-end library or framework, or build custom components, for any project size."
      - "Built on cutting-edge technology to keep your projects up-to-date with the latest web standards."
    button:
      enable: false
      label: ""
      link: ""
---
