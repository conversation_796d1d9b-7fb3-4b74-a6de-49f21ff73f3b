@import "tailwindcss";
@plugin "@tailwindcss/forms";
@plugin "@tailwindcss/typography";
@plugin "./tailwind-plugin/tw-theme";
@plugin "./tailwind-plugin/tw-bs-grid";
@source "hugo_stats.json";

@custom-variant dark (&:where(.dark, .dark *));

@import "./safe.css";
@import "./utilities.css";

@layer base {
  @import "./base.css";
}

@layer components {
  @import "./components.css";
  @import "./navigation.css";
  @import "./buttons.css";
}

@import "search.css";
@import "social-share.css";
@import "gallery-slider.css";
@import "images.css";
@import "toc.css";
@import "tab.css";
@import "accordion.css";
@import "modal.css";
@import "notice.css";

@import "module-overrides.css";
@import "custom.css";
