{{ define "main" }}
  <!-- Banner -->
  {{ with .Params.banner }}
    <section class="section pt-14">
      <div class="container">
        <div class="row justify-center">
          <div class="lg:col-7 md:col-9 mb-8 text-center">
            <h1 class="mb-4 text-h3 lg:text-h1">
              {{ .title | markdownify }}
            </h1>
            <p class="mb-8">
              {{ .content | markdownify }}
            </p>
            {{ with .button }}
              {{ if .enable }}
                <a
                  class="btn btn-primary"
                  href="{{ .link | absURL }}"
                  {{ if strings.HasPrefix .link `http` }}
                    target="_blank" rel="noopener"
                  {{ end }}>
                  {{ .label }}
                  <i class="fa fa-arrow-right pl-2"></i>
                </a>
              {{ end }}
            {{ end }}
          </div>
          <div class="col-12">
            {{ partial "image" (dict "Src" .image "Alt" "Banner image" "Loading" "eager" "Class" "mx-auto lg:!max-w-[800px]" "DisplayXL" "800x" ) }}
          </div>
        </div>
      </div>
    </section>
  {{ end }}
  <!-- /Banner -->

  <!-- Features -->
  {{ range $i, $e:= .Params.features }}
    <section class="section-sm {{ if (modBool $i 2) }}bg-gradient{{ end }}">
      <div class="container">
        <div class="row items-center justify-between">
          <div
            class="mb:md-0 {{ if not (modBool $i 2) }}
              md:order-2
            {{ end }} md:col-5 mb-6">
            {{ partial "image" (dict "Src" .image "Alt" "feature image" "DisplayXL" "520x" "DisplayLG" "425x" "DisplayMD" "360x") }}
          </div>
          <div
            class="{{ if not (modBool $i 2) }}
              md:order-1
            {{ end }} md:col-7 lg:col-6">
            <h2 class="mb-4">
              {{ .title | markdownify }}
            </h2>
            <p class="mb-8 text-lg">
              {{ .content | markdownify }}
            </p>
            <ul>
              {{ range .bulletpoints }}
                <li class="relative mb-4 pl-6">
                  <i class="fa fa-check absolute left-0 top-1.5"></i>
                  {{ . | markdownify }}
                </li>
              {{ end }}
            </ul>
            {{ with .button }}
              {{ if .enable }}
                <a class="btn btn-primary mt-6" href="{{ .link | absURL }}">
                  {{ .label }}
                  <i class="fa fa-arrow-right pl-2"></i>
                </a>
              {{ end }}
            {{ end }}
          </div>
        </div>
      </div>
    </section>
  {{ end }}
  <!-- /Features -->

  <!-- Testimonials -->
  {{ with site.GetPage "sections/testimonial" }}
    {{ if .Params.enable }}
      <section class="section">
        <div class="container">
          <div class="row">
            <div class="md:col-10 lg:col-8 xl:col-6 mx-auto mb-12 text-center">
              <h2 class="mb-4">
                {{ .Title | markdownify }}
              </h2>
              <p>
                {{ .Params.description | markdownify }}
              </p>
            </div>
            <div class="col-12">
              <div class="swiper testimonial-slider">
                <div class="swiper-wrapper">
                  {{ range .Params.testimonials }}
                    <div class="swiper-slide">
                      <div
                        class="bg-light dark:bg-darkmode-light rounded-lg px-7 py-10">
                        <div class="text-text-dark dark:text-white">
                          <svg
                            width="33"
                            height="20"
                            viewBox="0 0 33 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M1.28375 19.41L0.79375 18.64C1.21375 17.0067 1.75042 15.07 2.40375 12.83C3.05708 10.5433 3.75708 8.28 4.50375 6.04C5.29708 3.75333 6.06708 1.77 6.81375 0.0899959H15.3538C14.9338 2.09666 14.4904 4.26667 14.0238 6.6C13.5571 8.88666 13.1371 11.15 12.7638 13.39C12.4371 15.5833 12.1571 17.59 11.9238 19.41H1.28375ZM31.69 0.0899959L32.18 0.859998C31.76 2.54 31.2233 4.5 30.57 6.74C29.9167 8.98 29.2167 11.2433 28.47 13.53C27.7233 15.77 26.9533 17.73 26.16 19.41H17.69C18.0167 17.9167 18.3433 16.33 18.67 14.65C18.9967 12.9233 19.3 11.22 19.58 9.54C19.9067 7.81333 20.1867 6.15667 20.42 4.57C20.7 2.93666 20.91 1.44333 21.05 0.0899959H31.69Z"
                              fill="currentColor" />
                          </svg>
                        </div>
                        <blockquote class="mt-8">
                          {{ .content | markdownify }}
                        </blockquote>
                        <div class="mt-11 flex items-center">
                          <div class="text-text-dark dark:text-white">
                            {{ partial "image" (dict "Src" .avatar "Size" "50x50" "Class" "rounded-full" "Alt" .name) }}
                          </div>
                          <div class="ml-4">
                            <h3 class="h5 font-primary font-semibold">
                              {{ .name }}
                            </h3>
                            <p class="text-text-dark dark:text-white">
                              {{ .designation | markdownify }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  {{ end }}
                </div>
                <div
                  class="testimonial-slider-pagination mt-9 flex items-center justify-center text-center"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
    {{ end }}
  {{ end }}
  <!-- /Testimonials -->
{{ end }}
