<div class="bg-body dark:bg-darkmode-body">
  {{ $image:= .Params.image }}
  {{ if $image }}
    {{ partial "image" (dict "Src" $image "Context" .Page "Alt" .Title "Class" "mb-6 w-full rounded") }}
  {{ end }}
  <h4 class="mb-3">
    <a href="{{ .RelPermalink }}">
      {{ .Title }}
    </a>
  </h4>
  {{ $categories:= .Params.categories }}
  {{ if $categories }}
    <ul class="mb-4">
      <li class="mr-4 inline-block">
        <a href="{{ `authors/` | relLangURL }}{{ .Params.Author | urlize }}/">
          <i class="fa-regular fa-circle-user mr-2"></i>{{ .Params.author }}
        </a>
      </li>
      <li class="mr-4 inline-block">
        <i class="fa-regular fa-folder mr-1"></i>
        {{ range $i,$p:= $categories }}
          <a
            href="{{ `categories/` | relLangURL }}{{ . | urlize | lower }}/"
            class="ms-1"
            >{{ . | humanize }}{{ if ne $i (sub (len $categories) 1) }}
              {{ "," }}
            {{ end }}
          </a>
        {{ end }}
      </li>
    </ul>
  {{ end }}
  <p class="mb-6">{{ .Summary | plainify }}</p>
  <a class="btn btn-outline-primary btn-sm" href="{{ .RelPermalink }}">
    {{ T "read_more" }}
  </a>
</div>
