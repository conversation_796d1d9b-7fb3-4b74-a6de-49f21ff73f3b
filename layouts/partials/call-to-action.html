<!-- Call to actions -->
{{ with site.GetPage "sections/call-to-action" }}
  {{ if .Params.enable }}
    <section class="mb-28">
      <div class="container">
        <div
          class="bg-light dark:bg-darkmode-light rounded-xl px-4 py-16 xl:p-20">
          <div class="row items-center justify-between">
            <div class="md:col-5 lg:col-4 mb-10 md:order-2 md:mb-0">
              {{ partial "image" (dict "Src" .image "Alt" "call to action" "Class" "w-full") }}
            </div>
            <div class="md:col-7 md:order-1">
              <h2 class="mb-2">
                {{ .Title | markdownify }}
              </h2>
              <p class="mb-6">
                {{ .Params.description | markdownify }}
              </p>
              {{ with .Params.button }}
                {{ if .enable }}
                  <a class="btn btn-primary" href="{{ .link | absURL }}">
                    {{ .label }}
                  </a>
                {{ end }}
              {{ end }}
            </div>
          </div>
        </div>
      </div>
    </section>
  {{ end }}
{{ end }}
<!-- /Call to actions -->
