{{ define "main" }}
  {{ partial "page-header" . }}


  <section class="section">
    <div class="container">
      <div class="row gx-5">
        <!-- blog posts -->
        <div class="lg:col-8">
          <div class="row">
            {{ $paginator:= .Paginate .RegularPages }}
            {{ range $paginator.Pages }}
              <div class="md:col-6 mb-14">
                {{ partial "components/blog-card" . }}
              </div>
            {{ end }}
          </div>
          {{ partial "components/pagination.html" . }}
        </div>
        <!-- sidebar -->
        <div class="lg:col-4">
          <!-- widget -->
          {{ $widget:= site.Params.widgets.sidebar }}
          {{ partialCached "widgets/widget-wrapper" ( dict "Widgets" $widget "Scope" . ) }}
        </div>
      </div>
    </div>
  </section>
{{ end }}
